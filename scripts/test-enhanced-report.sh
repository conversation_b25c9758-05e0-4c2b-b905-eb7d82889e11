#!/bin/bash
# Test script for enhanced report generation

echo "🧪 Testing Enhanced Report Generation..."

# Check if Code2Prompt is available
if ! command -v code2prompt &> /dev/null; then
    echo "❌ Code2Prompt not found. Please install it first."
    exit 1
fi

# Test with current project
echo "📊 Analyzing current project with Code2Prompt..."
export LC_ALL=C.UTF-8
export LANG=C.UTF-8
code2prompt . \
    --output-file /tmp/project-analysis.txt \
    --exclude "node_modules/**,dist/**,build/**,*.log,.git/**" \
    --line-numbers \
    --tokens format

if [ -f /tmp/project-analysis.txt ]; then
    echo "✅ Code2Prompt analysis complete!"
    echo "📄 Output saved to /tmp/project-analysis.txt"
    echo "📊 File size: $(du -h /tmp/project-analysis.txt | cut -f1)"
    echo "📝 Line count: $(wc -l < /tmp/project-analysis.txt)"
else
    echo "❌ Code2Prompt analysis failed"
fi
