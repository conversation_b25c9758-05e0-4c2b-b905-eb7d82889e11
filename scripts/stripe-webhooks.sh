#!/bin/bash
# Stripe webhook testing script

echo "💳 Stripe Webhook Testing Setup"

# Check if Stripe CLI is authenticated
if ! stripe config --list &>/dev/null; then
    echo "🔐 Stripe CLI not authenticated. Please run:"
    echo "    stripe login"
    echo ""
    echo "Then run this script again."
    exit 1
fi

echo "✅ Stripe CLI is authenticated"

# Check if development server is running
if ! curl -s http://localhost:5000/api/health &>/dev/null; then
    echo "⚠️  Development server not running. Starting it..."
    echo "Please run 'npm run dev' in another terminal first."
    exit 1
fi

echo "✅ Development server is running"

# Start webhook forwarding
echo "🔗 Starting webhook forwarding to localhost:5000/api/report/webhook"
echo "📝 Copy the webhook secret that appears below to your .env file as STRIPE_CLI_WEBHOOK_SECRET"
echo ""
echo "Press Ctrl+C to stop webhook forwarding"
echo ""

stripe listen --forward-to localhost:5000/api/report/webhook
