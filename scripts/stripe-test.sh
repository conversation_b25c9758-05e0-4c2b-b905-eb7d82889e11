#!/bin/bash
# Stripe testing commands

echo "💳 Stripe Testing Commands"
echo ""

if ! command -v stripe &> /dev/null; then
    echo "❌ Stripe CLI not found"
    exit 1
fi

echo "Available test commands:"
echo ""
echo "1. 🔐 Login to Stripe:"
echo "   stripe login"
echo ""
echo "2. 🔗 Start webhook forwarding:"
echo "   ./scripts/stripe-webhooks.sh"
echo ""
echo "3. 💰 Trigger test payment events:"
echo "   stripe trigger payment_intent.succeeded"
echo "   stripe trigger payment_intent.payment_failed"
echo "   stripe trigger checkout.session.completed"
echo ""
echo "4. 📊 View recent events:"
echo "   stripe events list --limit 10"
echo ""
echo "5. 🧪 Test webhook endpoint:"
echo "   curl -X POST http://localhost:5000/api/report/webhook \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"type\": \"test.event\"}'"
echo ""
echo "6. 📋 List webhook endpoints:"
echo "   stripe webhook_endpoints list"
echo ""

# Interactive menu
echo "Choose an option (1-6) or press Enter to exit:"
read -r choice

case $choice in
    1)
        stripe login
        ;;
    2)
        ./scripts/stripe-webhooks.sh
        ;;
    3)
        echo "Choose event to trigger:"
        echo "a) payment_intent.succeeded"
        echo "b) payment_intent.payment_failed"
        echo "c) checkout.session.completed"
        read -r event_choice
        case $event_choice in
            a) stripe trigger payment_intent.succeeded ;;
            b) stripe trigger payment_intent.payment_failed ;;
            c) stripe trigger checkout.session.completed ;;
            *) echo "Invalid choice" ;;
        esac
        ;;
    4)
        stripe events list --limit 10
        ;;
    5)
        curl -X POST http://localhost:5000/api/report/webhook \
             -H 'Content-Type: application/json' \
             -d '{"type": "test.event"}'
        ;;
    6)
        stripe webhook_endpoints list
        ;;
    *)
        echo "Exiting..."
        ;;
esac
