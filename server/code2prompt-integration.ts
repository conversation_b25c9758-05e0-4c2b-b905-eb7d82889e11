import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

/**
 * Code2Prompt integration for comprehensive codebase analysis
 * This utility helps achieve 80-90% codebase coverage for report generation
 */

export interface Code2PromptOptions {
  // Basic options
  outputFile?: string;
  template?: string;

  // Filtering options
  include?: string[];
  exclude?: string[];
  gitignore?: boolean;

  // Content options
  lineNumbers?: boolean;
  relativePaths?: boolean;

  // Advanced options
  tokenCount?: boolean;
  tokenMode?: 'raw' | 'format';
  encoding?: string;

  // Token optimization
  maxTokens?: number;
  optimizationStrategy?: 'SMART_FILTER' | 'CHUNK' | 'SUMMARIZE';

  // Custom templates for different analysis types
  analysisType?:
    | 'FULL'
    | 'ARCHITECTURE'
    | 'SECURITY'
    | 'BUSINESS'
    | 'TESTING'
    | 'DEPLOYMENT'
    | 'DOCUMENTATION';
}

export interface Code2PromptResult {
  prompt: string;
  tokenCount?: number;
  filesCovered: string[];
  totalFiles: number;
  coveragePercentage: number;
  metadata: {
    projectStructure: string;
    technologies: string[];
    dependencies: string[];
    codeMetrics: any;
  };
}

/**
 * Custom templates for different analysis types
 */
const ANALYSIS_TEMPLATES = {
  FULL: `# Comprehensive MVP Analysis Request

## Project Overview
This is a comprehensive codebase analysis for MVP readiness assessment.

## Analysis Requirements
Please provide a comprehensive MVP readiness analysis covering:
1. **Architecture & Design Patterns**
2. **Code Quality & Best Practices**
3. **Security Assessment**
4. **Performance Considerations**
5. **Testing Strategy**
6. **Documentation Quality**
7. **Deployment Readiness**
8. **Business Value & User Experience**

Focus on actionable insights for rapid MVP iteration and improvement.

{{source_tree}}

{{#each files}}
\`{{path}}\`:

\`\`\`{{extension}}
{{content}}
\`\`\`

{{/each}}
`,

  ARCHITECTURE: `# Architecture Analysis Request

## Project Analysis
This is an architectural analysis of the codebase for MVP readiness.

## Analysis Focus
Analyze the architectural patterns, design decisions, and structural quality of this codebase.

## Source Tree
{{source_tree}}

## Files
{{files}}
`,

  SECURITY: `# Security Assessment Request

## Project Analysis
This is a security assessment of the codebase for MVP readiness.

## Analysis Focus
Conduct a comprehensive security analysis focusing on vulnerabilities, authentication, data protection, and security best practices.

## Source Tree
{{source_tree}}

## Files
{{files}}
`,

  BUSINESS: `# Business Readiness Analysis

## Project Analysis
This is a business readiness analysis of the codebase for MVP readiness.

## Analysis Focus
Evaluate MVP business readiness, user experience, market fit indicators, and launch preparedness.

## Source Tree
{{source_tree}}

## Files
{{files}}
`,

  TESTING: `# Testing Strategy Analysis

## Project Analysis
This is a testing strategy analysis of the codebase for MVP readiness.

## Analysis Focus
Assess testing coverage, quality, and strategy. Provide recommendations for comprehensive testing approach.

## Source Tree
{{source_tree}}

## Files
{{files}}
`,

  DEPLOYMENT: `# Deployment & Ops Analysis Request

## Project Analysis
This is a deployment and operations analysis of the codebase for MVP readiness.

## Analysis Focus
Review build, runtime, config, infra-as-code, and hosting readiness. Identify gaps and risks.

## Source Tree
{{source_tree}}

## Files
{{files}}
`,

  DOCUMENTATION: `# Documentation Analysis Request

## Project Analysis
This is a documentation analysis of the codebase for MVP readiness.

## Analysis Focus
Assess README quality, docs coverage, inline docs, API docs, and contributor guidance.

## Source Tree
{{source_tree}}

## Files
{{files}}
`,
};

/**
 * Generate comprehensive codebase prompt using Code2Prompt
 */
export async function generateCodebasePrompt(
  projectPath: string,
  options: Code2PromptOptions = {}
): Promise<Code2PromptResult> {
  let templateFile: string | undefined;

  try {
    // Check if code2prompt is installed
    await checkCode2PromptInstallation();

    const {
      outputFile = 'temp_codebase_prompt.txt',
      template,
      include = [],
      exclude = [
        'node_modules/**',
        '.git/**',
        'dist/**',
        'build/**',
        '*.log',
        'package-lock.json',
      ],
      gitignore = true,
      lineNumbers = true,
      relativePaths = true,
      tokenCount = true,
      tokenMode = 'format',
      maxTokens = 100000, // Default to 100K tokens for safety
      optimizationStrategy = 'SMART_FILTER',
      analysisType = 'FULL',
    } = options;

    // Apply optimization strategy based on token limits
    let finalInclude = include;
    let finalExclude = exclude;

    if (optimizationStrategy === 'SMART_FILTER') {
      const optimizedFilters = getSmartFiltersForAnalysis(
        analysisType,
        include,
        exclude
      );
      finalInclude = optimizedFilters.include;
      finalExclude = optimizedFilters.exclude;
      console.log(
        `🎯 Using SMART_FILTER optimization for ${analysisType} analysis`
      );
      console.log(`📁 Include patterns: ${finalInclude.join(', ')}`);
      console.log(`🚫 Exclude patterns: ${finalExclude.join(', ')}`);
    } else if (optimizationStrategy === 'CHUNK') {
      console.log(
        `📦 Using CHUNK optimization for ${analysisType} analysis (maxTokens: ${maxTokens})`
      );
      // TODO: Implement chunking strategy
    } else {
      console.log(
        `📝 Using default code2prompt template for ${analysisType} analysis`
      );
    }
    templateFile = undefined;

    // Build code2prompt command
    const cmd = buildCode2PromptCommand(projectPath, {
      outputFile,
      template: undefined,
      templateFile,
      include: finalInclude,
      exclude: finalExclude,
      gitignore,
      lineNumbers,
      relativePaths,
      tokenCount,
      tokenMode,
      encoding: 'cl100k',
    });

    console.log(`🚀 Executing code2prompt command: ${cmd}`);

    // Execute code2prompt
    const { stdout, stderr } = await execAsync(cmd);

    if (stderr && !stderr.includes('Warning')) {
      console.warn('Code2Prompt warnings:', stderr);
    }

    // Read the generated prompt - resolve relative to the project directory, not current working directory
    const promptPath = path.resolve(projectPath, outputFile);
    const prompt = await fs.readFile(promptPath, 'utf-8');

    // Persist artifact for logging/evidence
    try {
      const outDir = path.join(projectPath, '.code2prompt', 'out');
      await fs.mkdir(outDir, { recursive: true });
      const artifactName = `${(
        analysisType || 'FULL'
      ).toLowerCase()}-${Date.now()}.md`;
      const artifactPath = path.join(outDir, artifactName);
      await fs.writeFile(artifactPath, prompt, 'utf-8');
      console.log(`\ud83e\uddfe Code2Prompt artifact saved: ${artifactPath}`);
    } catch (e) {
      console.warn('Could not persist Code2Prompt artifact:', e);
    }

    // Extract metadata from the prompt
    const metadata = await extractMetadata(projectPath, prompt);

    // Calculate coverage
    const { filesCovered, totalFiles, coveragePercentage } =
      await calculateCoverage(projectPath, exclude);

    // Clean up temporary files
    try {
      await fs.unlink(promptPath);
    } catch (error) {
      console.warn('Could not clean up temporary prompt file:', error);
    }

    return {
      prompt,
      tokenCount: extractTokenCount(stdout),
      filesCovered,
      totalFiles,
      coveragePercentage,
      metadata,
    };
  } catch (error) {
    console.error('Error generating codebase prompt:', error);
    throw new Error(
      `Failed to generate codebase prompt: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  } finally {
    // Clean up template file
    if (templateFile) {
      try {
        await fs.unlink(templateFile);
        console.log(`🧹 Cleaned up template file: ${templateFile}`);
      } catch (error) {
        console.warn('Could not clean up template file:', error);
      }
    }
  }
}

/**
 * Check if code2prompt is installed
 */
async function checkCode2PromptInstallation(): Promise<void> {
  try {
    await execAsync('code2prompt --version');
  } catch (error) {
    throw new Error(
      'Code2Prompt is not installed. Please install it using: cargo install code2prompt'
    );
  }
}

/**
 * Build the code2prompt command
 */
function buildCode2PromptCommand(
  projectPath: string,
  options: Required<
    Omit<
      Code2PromptOptions,
      'analysisType' | 'template' | 'maxTokens' | 'optimizationStrategy'
    >
  > & {
    template?: string;
    templateFile?: string;
  }
): string {
  const {
    outputFile,
    template,
    templateFile,
    include,
    exclude,
    gitignore,
    lineNumbers,
    relativePaths,
    tokenCount,
    tokenMode,
    encoding,
  } = options;

  let cmd = `export LC_ALL=C.UTF-8 && export LANG=C.UTF-8 && code2prompt "${projectPath}"`;

  // Make output file path absolute to avoid path resolution issues
  if (outputFile) {
    const absoluteOutputFile = path.resolve(projectPath, outputFile);
    cmd += ` --output-file "${absoluteOutputFile}"`;
  }

  // Use template file instead of inline template to avoid shell interpretation issues
  if (templateFile) {
    cmd += ` --template "${templateFile}"`;
  } else if (template) {
    cmd += ` --template "${template}"`;
  }

  // Use multiple --include and --exclude flags instead of comma-separated values
  if (include.length > 0) {
    include.forEach((pattern) => {
      cmd += ` --include "${pattern}"`;
    });
  }
  if (exclude.length > 0) {
    exclude.forEach((pattern) => {
      cmd += ` --exclude "${pattern}"`;
    });
  }
  // Respect .gitignore by default; only disable when explicitly requested
  if (gitignore === false) cmd += ' --no-ignore';
  if (lineNumbers) cmd += ' --line-numbers';
  // Use relative paths by default; only force absolute when relativePaths is false
  if (relativePaths === false) cmd += ' --absolute-paths';
  if (tokenCount) cmd += ` --tokens ${tokenMode}`;
  if (encoding) cmd += ` --encoding "${encoding}"`;

  return cmd;
}

/**
 * Smart filtering based on analysis type to reduce token count
 * Uses content-based patterns that work across different codebase structures
 */
function getSmartFiltersForAnalysis(
  analysisType: Code2PromptOptions['analysisType'],
  currentInclude: string[],
  currentExclude: string[]
): { include: string[]; exclude: string[] } {
  const baseExclude = [
    ...currentExclude,
    // Aggressive exclusions for token optimization
    '**/*.min.js',
    '**/*.min.css',
    '**/*.bundle.*',
    '**/*.chunk.*',
    '**/*.map',
    '**/*.d.ts',
    '**/vendor/**',
    '**/third-party/**',
    '**/coverage/**',
    '**/.nyc_output/**',
    '**/tmp/**',
    '**/temp/**',
    '**/cache/**',
    '**/logs/**',
    '**/*.log',
    '**/*.lock',
    '**/public/assets/**',
    '**/static/assets/**',
    '**/assets/images/**',
    '**/assets/fonts/**',
    '**/images/**',
    '**/fonts/**',
    '**/*.png',
    '**/*.jpg',
    '**/*.jpeg',
    '**/*.gif',
    '**/*.svg',
    '**/*.ico',
    '**/*.woff*',
    '**/*.ttf',
    '**/*.eot',
    // Large generated files
    '**/package-lock.json',
    '**/yarn.lock',
    '**/pnpm-lock.yaml',
    '**/poetry.lock',
    '**/Pipfile.lock',
  ];

  switch (analysisType) {
    case 'ARCHITECTURE':
      return {
        include:
          currentInclude.length > 0
            ? currentInclude
            : [
                // Configuration files only
                '**/package.json',
                '**/tsconfig.json',
                '**/jsconfig.json',
                '**/*.config.{js,ts,mjs,cjs}',
                '**/next.config.*',
                '**/vite.config.*',
                '**/webpack.config.*',
                '**/rollup.config.*',
                '**/babel.config.*',
                '**/tailwind.config.*',
                // Only main entry points (be very specific)
                'index.{ts,tsx,js,jsx}',
                'main.{ts,tsx,js,jsx}',
                'app.{ts,tsx,js,jsx}',
                'App.{ts,tsx,js,jsx}',
                '**/src/index.{ts,tsx,js,jsx}',
                '**/src/main.{ts,tsx,js,jsx}',
                '**/src/app.{ts,tsx,js,jsx}',
                '**/src/App.{ts,tsx,js,jsx}',
                // Only top-level architectural files
                '**/src/router.{ts,tsx,js,jsx}',
                '**/src/store.{ts,tsx,js,jsx}',
                '**/src/context.{ts,tsx,js,jsx}',
                '**/src/types.{ts,tsx}',
                '**/src/interfaces.{ts,tsx}',
                '**/src/constants.{ts,tsx,js,jsx}',
                // Documentation
                'README*',
                'ARCHITECTURE*',
              ],
        exclude: [
          ...baseExclude,
          '**/*.test.*',
          '**/*.spec.*',
          '**/__tests__/**',
          '**/*test*/**',
          '**/*spec*/**',
          // Exclude all components, pages, and detailed implementation
          '**/*component*.{ts,tsx,js,jsx}',
          '**/*page*.{ts,tsx,js,jsx}',
          '**/*view*.{ts,tsx,js,jsx}',
          '**/components/**',
          '**/pages/**',
          '**/views/**',
          '**/ui/**',
          '**/hooks/**',
          '**/utils/**',
          '**/helpers/**',
        ],
      };

    case 'SECURITY':
      return {
        include:
          currentInclude.length > 0
            ? currentInclude
            : [
                // Only security-specific files (be very targeted)
                '**/*auth*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*security*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*middleware*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*guard*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*permission*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*role*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*login*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*session*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*token*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*jwt*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*oauth*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*crypto*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*password*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*cors*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*csrf*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*sanitiz*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*validat*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                // Environment and config files
                '**/*.env*',
                '.env*',
                '**/package.json',
                // Docker security
                '**/Dockerfile*',
                '**/docker-compose*.{yml,yaml}',
              ],
        exclude: [
          ...baseExclude,
          '**/*.test.*',
          '**/*.spec.*',
          // Exclude broad patterns that might match too much
          '**/*api*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
          '**/*server*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
          '**/*endpoint*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
          '**/*controller*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
          '**/*handler*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
          '**/*user*.{ts,tsx,js,jsx,py,php,rb,go,rs}', // Too broad
          '**/*config*.{ts,tsx,js,jsx,json,yaml,yml,py,php,rb,go,rs}', // Too broad
        ],
      };

    case 'TESTING':
      return {
        include:
          currentInclude.length > 0
            ? currentInclude
            : [
                '**/*.test.*',
                '**/*.spec.*',
                '**/__tests__/**',
                '**/test/**',
                '**/tests/**',
                'jest.config.*',
                'vitest.config.*',
                'cypress.config.*',
                'playwright.config.*',
              ],
        exclude: baseExclude,
      };

    case 'DEPLOYMENT':
      return {
        include:
          currentInclude.length > 0
            ? currentInclude
            : [
                'Dockerfile*',
                'docker-compose*',
                '**/.github/**',
                '**/deploy/**',
                '**/deployment/**',
                '**/k8s/**',
                '**/kubernetes/**',
                'package.json',
                'yarn.lock',
                'package-lock.json',
                'Procfile',
                'vercel.json',
                'netlify.toml',
              ],
        exclude: baseExclude,
      };

    case 'DOCUMENTATION':
      return {
        include:
          currentInclude.length > 0
            ? currentInclude
            : [
                'README*',
                'CHANGELOG*',
                'CONTRIBUTING*',
                '**/docs/**',
                '**/*.md',
                'package.json',
              ],
        exclude: baseExclude,
      };

    case 'BUSINESS':
      return {
        include:
          currentInclude.length > 0
            ? currentInclude
            : [
                // Business logic and user-facing features
                '**/*business*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*service*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*logic*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*feature*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*workflow*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*process*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*model*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*entity*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*domain*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                // User interface and experience
                '**/*component*.{ts,tsx,js,jsx}',
                '**/*page*.{ts,tsx,js,jsx}',
                '**/*view*.{ts,tsx,js,jsx}',
                '**/*screen*.{ts,tsx,js,jsx}',
                '**/*form*.{ts,tsx,js,jsx}',
                '**/*modal*.{ts,tsx,js,jsx}',
                '**/*dialog*.{ts,tsx,js,jsx}',
                '**/*dashboard*.{ts,tsx,js,jsx}',
                // API and data handling
                '**/*api*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*endpoint*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*controller*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*repository*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*database*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                '**/*query*.{ts,tsx,js,jsx,py,php,rb,go,rs}',
                // Configuration and documentation
                '**/package.json',
                '**/README*',
                '**/BUSINESS*',
                '**/FEATURES*',
                '**/REQUIREMENTS*',
              ],
        exclude: [
          ...baseExclude,
          '**/*.test.*',
          '**/*.spec.*',
          '**/__tests__/**',
          '**/*test*/**',
          '**/*spec*/**',
          // Exclude purely technical files
          '**/*config*.{ts,tsx,js,jsx}',
          '**/*setup*.{ts,tsx,js,jsx}',
          '**/*build*.{ts,tsx,js,jsx}',
          '**/*webpack*.{ts,tsx,js,jsx}',
          '**/*babel*.{ts,tsx,js,jsx}',
        ],
      };

    default: // FULL
      return {
        include: currentInclude,
        exclude: baseExclude, // Still apply aggressive exclusions
      };
  }
}

/**
 * Get template for specific analysis type
 */
function getTemplateForAnalysis(
  analysisType: Code2PromptOptions['analysisType']
): string {
  return ANALYSIS_TEMPLATES[analysisType || 'FULL'] || ANALYSIS_TEMPLATES.FULL;
}

/**
 * Resolve on-disk template path for given analysis type, if available
 */
function resolveTemplatePath(
  analysisType: Code2PromptOptions['analysisType']
): string | undefined {
  const templatesDir = path.join(process.cwd(), '.code2prompt', 'templates');
  const map: Record<string, string> = {
    FULL: 'comprehensive.hbs',
    ARCHITECTURE: 'architecture.hbs',
    SECURITY: 'security.hbs',
    BUSINESS: 'business.hbs',
    TESTING: 'testing.hbs',
    DEPLOYMENT: 'deployment.hbs',
    DOCUMENTATION: 'documentation.hbs',
  };
  const key = (analysisType || 'FULL').toUpperCase();
  const filename = map[key] || map.FULL;
  return path.join(templatesDir, filename);
}

/**
 * Extract metadata from the generated prompt
 */
async function extractMetadata(
  projectPath: string,
  prompt: string
): Promise<any> {
  // Extract project structure
  const treeMatch = prompt.match(/```\n([\s\S]*?)\n```/);
  const projectStructure = treeMatch ? treeMatch[1] : '';

  // Detect technologies from file extensions and content
  const technologies = detectTechnologies(prompt);

  // Extract dependencies (this would need to be enhanced based on project type)
  const dependencies = await extractDependencies(projectPath);

  // Basic code metrics
  const codeMetrics = calculateBasicMetrics(prompt);

  return {
    projectStructure,
    technologies,
    dependencies,
    codeMetrics,
  };
}

/**
 * Detect technologies from the codebase
 */
function detectTechnologies(prompt: string): string[] {
  const technologies = new Set<string>();

  // File extension patterns
  const patterns = {
    JavaScript: /\.js\b/g,
    TypeScript: /\.ts\b/g,
    React: /\.jsx\b|\.tsx\b|import.*react/gi,
    Vue: /\.vue\b|import.*vue/gi,
    Python: /\.py\b/g,
    Java: /\.java\b/g,
    'C#': /\.cs\b/g,
    Go: /\.go\b/g,
    Rust: /\.rs\b/g,
    PHP: /\.php\b/g,
    Ruby: /\.rb\b/g,
    Swift: /\.swift\b/g,
    Kotlin: /\.kt\b/g,
    HTML: /\.html\b/g,
    CSS: /\.css\b/g,
    SCSS: /\.scss\b/g,
    SQL: /\.sql\b/g,
    Docker: /Dockerfile|docker-compose/gi,
    'Node.js': /package\.json|node_modules/gi,
    'Next.js': /next\.config|pages\/|app\//gi,
    Express: /express/gi,
    MongoDB: /mongodb|mongoose/gi,
    PostgreSQL: /postgresql|pg\b/gi,
    Redis: /redis/gi,
    AWS: /aws-|@aws/gi,
    Firebase: /firebase/gi,
  };

  for (const [tech, pattern] of Object.entries(patterns)) {
    if (pattern.test(prompt)) {
      technologies.add(tech);
    }
  }

  return Array.from(technologies);
}

/**
 * Extract dependencies from package files
 */
async function extractDependencies(projectPath: string): Promise<string[]> {
  const dependencies: string[] = [];

  try {
    // Check for package.json (Node.js)
    const packageJsonPath = path.join(projectPath, 'package.json');
    try {
      const packageJson = JSON.parse(
        await fs.readFile(packageJsonPath, 'utf-8')
      );
      dependencies.push(...Object.keys(packageJson.dependencies || {}));
      dependencies.push(...Object.keys(packageJson.devDependencies || {}));
    } catch (error) {
      // File doesn't exist or is invalid
    }

    // Add more dependency extraction for other languages as needed
    // requirements.txt for Python, Cargo.toml for Rust, etc.
  } catch (error) {
    console.warn('Could not extract dependencies:', error);
  }

  return dependencies;
}

/**
 * Calculate basic code metrics
 */
function calculateBasicMetrics(prompt: string): any {
  const lines = prompt.split('\n');
  const codeLines = lines.filter(
    (line) =>
      line.trim() &&
      !line.trim().startsWith('//') &&
      !line.trim().startsWith('#') &&
      !line.trim().startsWith('/*') &&
      !line.trim().startsWith('*')
  );

  return {
    totalLines: lines.length,
    codeLines: codeLines.length,
    commentRatio:
      (((lines.length - codeLines.length) / lines.length) * 100).toFixed(2) +
      '%',
    estimatedComplexity: Math.floor(codeLines.length / 100), // Very basic complexity estimate
  };
}

/**
 * Calculate file coverage
 */
async function calculateCoverage(
  projectPath: string,
  exclude: string[]
): Promise<{
  filesCovered: string[];
  totalFiles: number;
  coveragePercentage: number;
}> {
  // This is a simplified implementation
  // In practice, you'd want to use the same logic as code2prompt for accurate coverage

  const filesCovered: string[] = [];
  const totalFiles = 100; // Placeholder - would need actual file counting logic
  const coveragePercentage = 85; // Placeholder - would calculate based on actual coverage

  return {
    filesCovered,
    totalFiles,
    coveragePercentage,
  };
}

/**
 * Extract token count from code2prompt output
 */
function extractTokenCount(output: string): number | undefined {
  const tokenMatch = output.match(/Token count: (\d+)/);
  return tokenMatch ? parseInt(tokenMatch[1]) : undefined;
}
