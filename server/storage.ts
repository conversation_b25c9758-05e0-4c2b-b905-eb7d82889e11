import { analyses, type Analysis, type InsertAnalysis, type User, type InsertGoogleUser } from "@shared/schema";

export interface IStorage {
  createAnalysis(analysis: InsertAnalysis): Promise<number>;
  getAnalysis(id: number): Promise<Analysis | undefined>;
  updateAnalysisStatus(
    id: number,
    status: 'processing' | 'completed' | 'failed',
    progress?: any
  ): Promise<void>;
  updateAnalysisResults(id: number, results: any): Promise<void>;
  updateExtractedPath(id: number, extractedPath: string): Promise<void>;
  listAnalyses(): Promise<Analysis[]>;

  // Premium report methods
  updateFullReport(id: number, fullReport: any): Promise<void>;
  updateReportUrl(id: number, reportUrl: string): Promise<void>;
  updateReportObjectKey(id: number, objectKey: string): Promise<void>;
  updateUploadedFileKey(id: number, fileKey: string): Promise<void>;
  updatePaymentStatus(
    id: number,
    status: 'pending' | 'completed',
    userId?: number
  ): Promise<void>;

  // User management methods for Google OAuth
  getUserByGoogleId(googleId: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserById(id: number): Promise<User | undefined>;
  createGoogleUser(userData: InsertGoogleUser): Promise<User>;
  linkGoogleAccount(userId: number, googleId: string): Promise<void>;
}

export class MemStorage implements IStorage {
  private analyses: Map<number, Analysis>;
  private users: Map<number, User>;
  private currentId: number;
  private currentUserId: number;

  constructor() {
    this.analyses = new Map();
    this.users = new Map();
    this.currentId = 1;
    this.currentUserId = 1;
  }

  async createAnalysis(analysis: InsertAnalysis): Promise<number> {
    const id = this.currentId++;

    const newAnalysis: Analysis = {
      id,
      type: analysis.type,
      repoUrl: analysis.repoUrl || null,
      description: analysis.description || null,
      projectType: analysis.projectType,
      status: analysis.status,
      progress: {
        stage: 'idle',
        currentStep: '',
        percentage: 0,
        steps: [
          { name: 'Extracting Files', status: 'pending' },
          { name: 'Parsing Files', status: 'pending' },
          { name: 'Analyzing Code', status: 'pending' },
          { name: 'Generating Report', status: 'pending' },
        ],
      },
      projectInfo: null,
      scoreData: null,
      fullReport: null,
      reportUrl: null,
      reportObjectKey: null,
      uploadedFileKey: null,
      extractedPath: null,
      paymentStatus: 'pending',
      userId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.analyses.set(id, newAnalysis);
    return id;
  }

  async getAnalysis(id: number): Promise<Analysis | undefined> {
    return this.analyses.get(id);
  }

  async updateAnalysisStatus(
    id: number,
    status: 'processing' | 'completed' | 'failed',
    progress?: any
  ): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.status = status;
    analysis.updatedAt = new Date();

    if (progress) {
      analysis.progress = progress;
    }

    this.analyses.set(id, analysis);
  }

  async updateAnalysisResults(id: number, results: any): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.status = 'completed';
    analysis.updatedAt = new Date();
    analysis.progress = results.progress;
    analysis.projectInfo = results.projectInfo;
    analysis.scoreData = results.scoreData;

    this.analyses.set(id, analysis);
  }

  async updateExtractedPath(id: number, extractedPath: string): Promise<void> {
    const analysis = this.analyses.get(id);
    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }
    analysis.extractedPath = extractedPath;
    analysis.updatedAt = new Date();
    this.analyses.set(id, analysis);
  }

  async listAnalyses(): Promise<Analysis[]> {
    return Array.from(this.analyses.values());
  }

  // Premium report methods implementation
  async updateFullReport(id: number, fullReport: any): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.fullReport = fullReport;
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }

  async updateReportUrl(id: number, reportUrl: string): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.reportUrl = reportUrl;
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }

  async updateReportObjectKey(id: number, objectKey: string): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.reportObjectKey = objectKey;
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }

  async updateUploadedFileKey(id: number, fileKey: string): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.uploadedFileKey = fileKey;
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }

  async updatePaymentStatus(
    id: number,
    status: 'pending' | 'completed',
    userId?: number
  ): Promise<void> {
    const analysis = this.analyses.get(id);

    if (!analysis) {
      throw new Error(`Analysis with ID ${id} not found`);
    }

    analysis.paymentStatus = status;
    if (userId) {
      analysis.userId = userId;
    }
    analysis.updatedAt = new Date();

    this.analyses.set(id, analysis);
  }

  // User management methods for Google OAuth
  async getUserByGoogleId(googleId: string): Promise<User | undefined> {
    for (const user of Array.from(this.users.values())) {
      if (user.googleId === googleId) {
        return user;
      }
    }
    return undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    for (const user of Array.from(this.users.values())) {
      if (user.email === email) {
        return user;
      }
    }
    return undefined;
  }

  async getUserById(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async createGoogleUser(userData: InsertGoogleUser): Promise<User> {
    const id = this.currentUserId++;
    const newUser: User = {
      id,
      username: null,
      password: null,
      googleId: userData.googleId || null,
      email: userData.email || null,
      name: userData.name || null,
      avatarUrl: userData.avatarUrl || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.users.set(id, newUser);
    return newUser;
  }

  async linkGoogleAccount(userId: number, googleId: string): Promise<void> {
    const user = this.users.get(userId);
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    user.googleId = googleId;
    user.updatedAt = new Date();
    this.users.set(userId, user);
  }
}

export const storage = new MemStorage();
