import fs from "fs";
import path from "path";
import os from "os";
import { promisify } from "util";
import { execFile } from "child_process";
import AdmZ<PERSON> from "adm-zip";
import axios from "axios";
import { analyzeProjectCode } from "./openai";
import { ProjectInfo, ProgressInfo, ScoreData } from "../client/src/pages/Home";
import { nanoid } from "nanoid";

import { log } from './vite';

const execFileAsync = promisify(execFile);

interface AnalysisOptions {
  analysisId: number;
  description: string;
  projectType: string;
}

/**
 * Analyzes an uploaded file from buffer (for object storage integration)
 */
export async function analyzeUploadedFileFromBuffer(
  fileBuffer: Buffer,
  originalFilename: string,
  options: AnalysisOptions
): Promise<{
  projectInfo: ProjectInfo;
  progress: ProgressInfo;
  scoreData: ScoreData;
  extractedPath?: string;
}> {
  // Create temporary file from buffer
  const tempDir = path.join(os.tmpdir(), `mvp-scoregen-${nanoid()}`);
  fs.mkdirSync(tempDir, { recursive: true });
  const tempFilePath = path.join(tempDir, originalFilename);

  try {
    // Write buffer to temporary file
    fs.writeFileSync(tempFilePath, fileBuffer);

    // Use existing file-based analyzer
    const result = await analyzeUploadedFile(tempFilePath, options);

    return result;
  } finally {
    // Clean up only the temporary file, not the extracted directory
    try {
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    } catch (err) {
      // Suppress noisy ENOENT when file was already removed downstream
      if ((err as any)?.code !== 'ENOENT') {
        console.error('Failed to clean up temporary file:', err);
      }
    }
  }
}

/**
 * Analyzes an uploaded ZIP/TAR file
 */
export async function analyzeUploadedFile(
  filePath: string,
  options: AnalysisOptions
): Promise<{
  projectInfo: ProjectInfo;
  progress: ProgressInfo;
  scoreData: ScoreData;
  extractedPath?: string;
}> {
  try {
    // Create temp folder for extraction
    const extractDir = path.join(path.dirname(filePath), 'extracted');
    fs.mkdirSync(extractDir, { recursive: true });

    // Extract the file contents
    const progress: ProgressInfo = {
      stage: 'analyzing',
      currentStep: 'Extracting Files',
      percentage: 10,
      steps: [
        { name: 'Extracting Files', status: 'current' },
        { name: 'Parsing Files', status: 'pending' },
        { name: 'Analyzing Code', status: 'pending' },
        { name: 'Generating Report', status: 'pending' },
      ],
    };

    const fileExtension = path.extname(filePath).toLowerCase();

    if (fileExtension === '.zip') {
      const zip = new AdmZip(filePath);
      zip.extractAllTo(extractDir, true);
    } else if (fileExtension === '.tar' || fileExtension === '.gz') {
      await execFileAsync('tar', ['-xf', filePath, '-C', extractDir]);
    }

    // Update progress
    progress.steps[0].status = 'completed';
    progress.steps[0].duration = '1.2s';
    progress.steps[1].status = 'current';
    progress.currentStep = 'Parsing Files';
    progress.percentage = 30;

    // Read all the files in the extracted directory
    const fileContents = await readDirectoryFiles(extractDir);

    // Count files
    const fileCount = Object.keys(fileContents).length;

    // Update progress
    progress.steps[1].status = 'completed';
    progress.steps[1].duration = '2.1s';
    progress.steps[2].status = 'current';
    progress.currentStep = 'Analyzing Code';
    progress.percentage = 60;

    // Analyze the code using AI
    const analysisResult = await analyzeProjectCode(
      fileContents,
      options.projectType,
      options.description
    );

    // Update progress
    progress.steps[2].status = 'completed';
    progress.steps[2].duration = '5.3s';
    progress.steps[3].status = 'current';
    progress.currentStep = 'Generating Report';
    progress.percentage = 90;

    // Generate radar chart data
    const radarData = {
      labels: [
        'Core Functionality',
        'User-Centric Design',
        'Technical Stability',
        'Feedback Collection',
        'Minimum Viability',
        'Launch Readiness',
        'Documentation',
        'Testing',
      ],
      datasets: [
        {
          label: 'Your Score',
          data: [
            analysisResult.criteriaScores.coreFunctionality,
            analysisResult.criteriaScores.userCentricDesign,
            analysisResult.criteriaScores.technicalStability,
            analysisResult.criteriaScores.feedbackCollection,
            analysisResult.criteriaScores.minimumViability,
            analysisResult.criteriaScores.launchReadiness,
            analysisResult.criteriaScores.documentation,
            analysisResult.criteriaScores.testing,
          ],
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          borderColor: 'rgba(59, 130, 246, 1)',
          pointBackgroundColor: 'rgba(59, 130, 246, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(59, 130, 246, 1)',
        },
        {
          label: 'MVP Threshold',
          data: [3, 3, 3, 3, 3, 3, 3, 3],
          backgroundColor: 'rgba(156, 163, 175, 0.1)',
          borderColor: 'rgba(156, 163, 175, 0.6)',
          pointBackgroundColor: 'rgba(156, 163, 175, 0.6)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(156, 163, 175, 1)',
          borderDash: [5, 5],
        },
      ],
    };

    // Calculate total score
    const totalScore =
      analysisResult.criteriaScores.coreFunctionality +
      analysisResult.criteriaScores.userCentricDesign +
      analysisResult.criteriaScores.technicalStability +
      analysisResult.criteriaScores.feedbackCollection +
      analysisResult.criteriaScores.minimumViability +
      analysisResult.criteriaScores.launchReadiness +
      analysisResult.criteriaScores.documentation +
      analysisResult.criteriaScores.testing;

    // Determine score category and color
    let category: string;
    let color: string;

    if (totalScore >= 36) {
      category = 'Advanced MVP (36-40 points)';
      color = '#10B981'; // Green
    } else if (totalScore >= 26) {
      category = 'Solid MVP (26-35 points)';
      color = '#10B981'; // Green
    } else if (totalScore >= 16) {
      category = 'Early MVP (16-25 points)';
      color = '#F59E0B'; // Amber
    } else {
      category = 'Pre-MVP (0-15 points)';
      color = '#EF4444'; // Red
    }

    // Format criterion scores for output
    const criterionScores = [
      {
        name: 'Core Functionality',
        description: 'Are essential features implemented and working?',
        score: analysisResult.criteriaScores.coreFunctionality,
        feedback: analysisResult.feedback.coreFunctionality,
      },
      {
        name: 'User-Centric Design',
        description: 'Is the UI intuitive and user-friendly?',
        score: analysisResult.criteriaScores.userCentricDesign,
        feedback: analysisResult.feedback.userCentricDesign,
      },
      {
        name: 'Technical Stability',
        description: 'Is the code reliable with minimal bugs?',
        score: analysisResult.criteriaScores.technicalStability,
        feedback: analysisResult.feedback.technicalStability,
      },
      {
        name: 'Feedback Collection',
        description: 'Are mechanisms in place to gather user input?',
        score: analysisResult.criteriaScores.feedbackCollection,
        feedback: analysisResult.feedback.feedbackCollection,
      },
      {
        name: 'Minimum Viability',
        description: 'Does it include only necessary features?',
        score: analysisResult.criteriaScores.minimumViability,
        feedback: analysisResult.feedback.minimumViability,
      },
      {
        name: 'Launch Readiness',
        description: 'Is it ready for a limited or public release?',
        score: analysisResult.criteriaScores.launchReadiness,
        feedback: analysisResult.feedback.launchReadiness,
      },
      {
        name: 'Documentation',
        description: 'Are there clear guides for users/developers?',
        score: analysisResult.criteriaScores.documentation,
        feedback: analysisResult.feedback.documentation,
      },
      {
        name: 'Testing',
        description: 'Has it been tested, ideally with users?',
        score: analysisResult.criteriaScores.testing,
        feedback: analysisResult.feedback.testing,
      },
    ];

    // Format recommendations with IDs
    const recommendations = analysisResult.recommendations.map(
      (rec, index) => ({
        id: index + 1,
        title: rec.title,
        description: rec.description,
        priority: rec.priority,
      })
    );

    // Update final progress
    progress.steps[3].status = 'completed';
    progress.steps[3].duration = '1.5s';
    progress.currentStep = 'Completed';
    progress.percentage = 100;
    progress.stage = 'completed';

    // Clean up only the uploaded file, preserve extracted directory for Code2Prompt
    try {
      fs.unlinkSync(filePath);
    } catch (err) {
      console.error('Failed to clean up uploaded file:', err);
    }

    // Return the results with extracted path for Code2Prompt
    return {
      projectInfo: {
        name: analysisResult.projectName,
        type: formatProjectType(options.projectType),
        fileCount,
        technologies: analysisResult.technologiesDetected,
      },
      progress,
      scoreData: {
        total: totalScore,
        percentage: (totalScore / 40) * 100,
        color,
        category,
        strengths: analysisResult.strengths,
        weaknesses: analysisResult.weaknesses,
        criterionScores,
        recommendations,
        radarData,
      },
      extractedPath: extractDir,
    };
  } catch (error) {
    console.error('Error analyzing file:', error);
    throw new Error(
      'Failed to analyze project: ' +
        (error instanceof Error ? error.message : 'Unknown error')
    );
  }
}

/**
 * Analyzes a GitHub repository
 */
export async function analyzeGithubRepo(
  repoUrl: string,
  options: AnalysisOptions
): Promise<{
  projectInfo: ProjectInfo;
  progress: ProgressInfo;
  scoreData: ScoreData;
  extractedPath?: string;
}> {
  try {
    // Parse GitHub URL
    const urlParts = repoUrl.replace('https://github.com/', '').split('/');
    if (urlParts.length < 2) {
      throw new Error('Invalid GitHub repository URL');
    }

    const owner = urlParts[0];
    const repo = urlParts[1];

    // Attempt to download and extract repo to a temporary directory for Code2Prompt
    let extractedPath: string | undefined = undefined;
    try {
      const repoMetaUrl = `https://api.github.com/repos/${owner}/${repo}`;
      const repoMeta = await axios.get(repoMetaUrl);
      const defaultBranch = repoMeta.data?.default_branch || 'main';
      const archiveUrl = `https://codeload.github.com/${owner}/${repo}/zip/refs/heads/${defaultBranch}`;

      const tempDir = path.join(os.tmpdir(), `mvp-github-${nanoid()}`);
      fs.mkdirSync(tempDir, { recursive: true });

      const zipResp = await axios.get(archiveUrl, {
        responseType: 'arraybuffer',
      });
      const zipPath = path.join(tempDir, `${repo}.zip`);
      fs.writeFileSync(zipPath, Buffer.from(zipResp.data));

      const zip = new AdmZip(zipPath);
      zip.extractAllTo(tempDir, true);

      // Determine extracted root folder (GitHub archives include repo-branch folder)
      const entries = fs.readdirSync(tempDir);
      const rootDir = entries
        .map((name) => path.join(tempDir, name))
        .find((p) => fs.existsSync(p) && fs.statSync(p).isDirectory());
      if (rootDir) {
        extractedPath = rootDir;
        log(
          `📦 GitHub repo extracted for Code2Prompt at: ${extractedPath}`,
          'express'
        );
      }
    } catch (extractErr) {
      log(
        '⚠️ Failed to extract GitHub repo for Code2Prompt. Will proceed without Code2Prompt: ' +
          (extractErr instanceof Error
            ? extractErr.message
            : String(extractErr)),
        'express'
      );
    }

    // Initialize progress
    const progress: ProgressInfo = {
      stage: 'analyzing',
      currentStep: 'Fetching Repository',
      percentage: 5,
      steps: [
        { name: 'Fetching Repository', status: 'current' },
        { name: 'Parsing Files', status: 'pending' },
        { name: 'Analyzing Code', status: 'pending' },
        { name: 'Generating Report', status: 'pending' },
      ],
    };

    // Get repository tree using GitHub API (lightweight analysis path)
    const apiUrl = `https://api.github.com/repos/${owner}/${repo}/contents`;

    // Update progress
    progress.steps[0].status = 'completed';
    progress.steps[0].duration = '2.3s';
    progress.steps[1].status = 'current';
    progress.currentStep = 'Parsing Files';
    progress.percentage = 30;

    // Get file contents (limit to avoid API rate limiting)
    const MAX_FILES = 20;
    const fileContents: { [path: string]: string } = {};
    let fileCount = 0;

    // Recursively get files from the repo, up to MAX_FILES
    await fetchRepoContents(apiUrl, fileContents, fileCount, MAX_FILES);

    // Update progress
    progress.steps[1].status = 'completed';
    progress.steps[1].duration = '3.5s';
    progress.steps[2].status = 'current';
    progress.currentStep = 'Analyzing Code';
    progress.percentage = 60;

    // Analyze the code using AI
    const analysisResult = await analyzeProjectCode(
      fileContents,
      options.projectType,
      options.description
    );

    // Update progress
    progress.steps[2].status = 'completed';
    progress.steps[2].duration = '5.3s';
    progress.steps[3].status = 'current';
    progress.currentStep = 'Generating Report';
    progress.percentage = 90;

    // Generate radar chart data
    const radarData = {
      labels: [
        'Core Functionality',
        'User-Centric Design',
        'Technical Stability',
        'Feedback Collection',
        'Minimum Viability',
        'Launch Readiness',
        'Documentation',
        'Testing',
      ],
      datasets: [
        {
          label: 'Your Score',
          data: [
            analysisResult.criteriaScores.coreFunctionality,
            analysisResult.criteriaScores.userCentricDesign,
            analysisResult.criteriaScores.technicalStability,
            analysisResult.criteriaScores.feedbackCollection,
            analysisResult.criteriaScores.minimumViability,
            analysisResult.criteriaScores.launchReadiness,
            analysisResult.criteriaScores.documentation,
            analysisResult.criteriaScores.testing,
          ],
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          borderColor: 'rgba(59, 130, 246, 1)',
          pointBackgroundColor: 'rgba(59, 130, 246, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(59, 130, 246, 1)',
        },
        {
          label: 'MVP Threshold',
          data: [3, 3, 3, 3, 3, 3, 3, 3],
          backgroundColor: 'rgba(156, 163, 175, 0.1)',
          borderColor: 'rgba(156, 163, 175, 0.6)',
          pointBackgroundColor: 'rgba(156, 163, 175, 0.6)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(156, 163, 175, 1)',
          borderDash: [5, 5],
        },
      ],
    };

    // Calculate total score
    const totalScore =
      analysisResult.criteriaScores.coreFunctionality +
      analysisResult.criteriaScores.userCentricDesign +
      analysisResult.criteriaScores.technicalStability +
      analysisResult.criteriaScores.feedbackCollection +
      analysisResult.criteriaScores.minimumViability +
      analysisResult.criteriaScores.launchReadiness +
      analysisResult.criteriaScores.documentation +
      analysisResult.criteriaScores.testing;

    // Determine score category and color
    let category: string;
    let color: string;

    if (totalScore >= 36) {
      category = 'Advanced MVP (36-40 points)';
      color = '#10B981'; // Green
    } else if (totalScore >= 26) {
      category = 'Solid MVP (26-35 points)';
      color = '#10B981'; // Green
    } else if (totalScore >= 16) {
      category = 'Early MVP (16-25 points)';
      color = '#F59E0B'; // Amber
    } else {
      category = 'Pre-MVP (0-15 points)';
      color = '#EF4444'; // Red
    }

    // Format criterion scores for output
    const criterionScores = [
      {
        name: 'Core Functionality',
        description: 'Are essential features implemented and working?',
        score: analysisResult.criteriaScores.coreFunctionality,
        feedback: analysisResult.feedback.coreFunctionality,
      },
      {
        name: 'User-Centric Design',
        description: 'Is the UI intuitive and user-friendly?',
        score: analysisResult.criteriaScores.userCentricDesign,
        feedback: analysisResult.feedback.userCentricDesign,
      },
      {
        name: 'Technical Stability',
        description: 'Is the code reliable with minimal bugs?',
        score: analysisResult.criteriaScores.technicalStability,
        feedback: analysisResult.feedback.technicalStability,
      },
      {
        name: 'Feedback Collection',
        description: 'Are mechanisms in place to gather user input?',
        score: analysisResult.criteriaScores.feedbackCollection,
        feedback: analysisResult.feedback.feedbackCollection,
      },
      {
        name: 'Minimum Viability',
        description: 'Does it include only necessary features?',
        score: analysisResult.criteriaScores.minimumViability,
        feedback: analysisResult.feedback.minimumViability,
      },
      {
        name: 'Launch Readiness',
        description: 'Is it ready for a limited or public release?',
        score: analysisResult.criteriaScores.launchReadiness,
        feedback: analysisResult.feedback.launchReadiness,
      },
      {
        name: 'Documentation',
        description: 'Are there clear guides for users/developers?',
        score: analysisResult.criteriaScores.documentation,
        feedback: analysisResult.feedback.documentation,
      },
      {
        name: 'Testing',
        description: 'Has it been tested, ideally with users?',
        score: analysisResult.criteriaScores.testing,
        feedback: analysisResult.feedback.testing,
      },
    ];

    // Format recommendations with IDs
    const recommendations = analysisResult.recommendations.map(
      (rec, index) => ({
        id: index + 1,
        title: rec.title,
        description: rec.description,
        priority: rec.priority,
      })
    );

    // Update final progress
    progress.steps[3].status = 'completed';
    progress.steps[3].duration = '1.5s';
    progress.currentStep = 'Completed';
    progress.percentage = 100;
    progress.stage = 'completed';

    // Return the results
    return {
      projectInfo: {
        name: analysisResult.projectName || repo,
        type: formatProjectType(options.projectType),
        fileCount: Object.keys(fileContents).length,
        technologies: analysisResult.technologiesDetected,
      },
      progress,
      scoreData: {
        total: totalScore,
        percentage: (totalScore / 40) * 100,
        color,
        category,
        strengths: analysisResult.strengths,
        weaknesses: analysisResult.weaknesses,
        criterionScores,
        recommendations,
        radarData,
      },
      extractedPath,
    };
  } catch (error) {
    console.error('Error analyzing GitHub repository:', error);
    throw new Error(
      'Failed to analyze GitHub repository: ' +
        (error instanceof Error ? error.message : 'Unknown error')
    );
  }
}

/**
 * Helper function to read and parse all files in a directory
 */
async function readDirectoryFiles(
  dir: string
): Promise<{ [path: string]: string }> {
  const fileContents: { [path: string]: string } = {};

  // Recursively read all files in the directory
  async function readDir(currentDir: string, basePath = '') {
    const files = fs.readdirSync(currentDir);

    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const relativePath = path.join(basePath, file);
      const stats = fs.statSync(filePath);

      if (stats.isDirectory()) {
        await readDir(filePath, relativePath);
      } else {
        // Check if it's a text file we should analyze
        const ext = path.extname(file).toLowerCase();
        const textFileExtensions = [
          '.js',
          '.ts',
          '.jsx',
          '.tsx',
          '.py',
          '.java',
          '.php',
          '.html',
          '.css',
          '.json',
          '.yml',
          '.yaml',
          '.md',
          '.txt',
          '.csv',
          '.xml',
          '.c',
          '.cpp',
          '.h',
          '.rb',
          '.go',
          '.rs',
          '.dart',
          '.swift',
          '.kt',
          '.sh',
        ];

        if (textFileExtensions.includes(ext) && stats.size < 1 * 1024 * 1024) {
          // Max 1MB per file
          try {
            fileContents[relativePath] = fs.readFileSync(filePath, 'utf8');
          } catch (err) {
            console.error(`Error reading file ${filePath}:`, err);
          }

          // Limit total number of files to avoid memory issues
          if (Object.keys(fileContents).length >= 100) {
            return;
          }
        }
      }
    }
  }

  await readDir(dir);
  return fileContents;
}

/**
 * Helper function to recursively fetch GitHub repository contents
 */
async function fetchRepoContents(
  url: string,
  fileContents: { [path: string]: string },
  fileCount: number,
  maxFiles: number,
  path = ''
): Promise<void> {
  if (fileCount >= maxFiles) return;

  try {
    const response = await axios.get(url);
    const contents = response.data;

    for (const item of contents) {
      if (fileCount >= maxFiles) break;

      const itemPath = path ? `${path}/${item.name}` : item.name;

      if (item.type === 'file') {
        // Check if it's a text file we should analyze
        const ext = itemPath.split('.').pop()?.toLowerCase();
        const textFileExtensions = [
          'js',
          'ts',
          'jsx',
          'tsx',
          'py',
          'java',
          'php',
          'html',
          'css',
          'json',
          'yml',
          'yaml',
          'md',
          'txt',
          'csv',
          'xml',
          'c',
          'cpp',
          'h',
          'rb',
          'go',
          'rs',
          'dart',
          'swift',
          'kt',
          'sh',
        ];

        if (textFileExtensions.includes(ext || '')) {
          try {
            const fileResponse = await axios.get(item.download_url);
            fileContents[itemPath] = fileResponse.data;
            fileCount++;
          } catch (err) {
            console.error(`Error fetching file ${itemPath}:`, err);
          }
        }
      } else if (item.type === 'dir') {
        await fetchRepoContents(
          item.url,
          fileContents,
          fileCount,
          maxFiles,
          itemPath
        );
      }
    }
  } catch (error) {
    console.error('Error fetching repo contents:', error);
  }
}

/**
 * Helper function to format project type for display
 */
function formatProjectType(projectType: string): string {
  switch (projectType) {
    case 'webapp':
      return 'Web Application';
    case 'mobile':
      return 'Mobile Application';
    case 'api':
      return 'API/Backend';
    case 'other':
      return 'Other';
    default:
      return projectType.charAt(0).toUpperCase() + projectType.slice(1);
  }
}
