import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';

async function testFullFlow() {
  console.log('🚀 Testing complete MVP analysis flow...\n');

  try {
    // Step 1: Upload and analyze the project
    console.log('📤 Step 1: Uploading test project...');

    const form = new FormData();
    form.append('file', fs.createReadStream('test-mvp-project.tar.gz'));
    form.append('projectType', 'web_app');
    form.append(
      'description',
      'A test MVP project for demonstrating analysis capabilities'
    );

    const uploadResponse = await axios.post(
      'http://localhost:5000/api/analyze/upload',
      form,
      {
        headers: {
          ...form.getHeaders(),
        },
        timeout: 60000,
      }
    );

    console.log('✅ Upload successful!');
    console.log('📊 Analysis ID:', uploadResponse.data.analysisId);
    console.log('📈 Score:', uploadResponse.data.scoreData.total, 'points');
    console.log('🏷️  Category:', uploadResponse.data.scoreData.category);

    const analysisId = uploadResponse.data.analysisId;

    // Step 2: Generate full report with enhanced context
    console.log('\n📝 Step 2: Generating full report with GPT-4o...');

    const reportResponse = await axios.post(
      `http://localhost:5000/api/report/generate/${analysisId}`,
      {
        userGoals:
          'Launch a successful MVP that can attract early users and validate the product-market fit',
        timeline: '3 months',
        experience: 'intermediate',
        targetAudience: 'developers and tech entrepreneurs',
        businessModel: 'freemium',
      },
      {
        timeout: 120000, // 2 minutes timeout for AI processing
      }
    );

    console.log('✅ Full report generated!');
    console.log(
      '📄 Report sections generated:',
      Object.keys(reportResponse.data.fullReport || {})
    );

    // Step 3: Get PDF URL (should be generated automatically)
    console.log('\n📄 Step 3: Getting PDF report URL...');

    const pdfUrl = reportResponse.data.data.reportUrl;

    console.log('✅ PDF URL obtained!');
    console.log('🔗 PDF URL:', pdfUrl);
    console.log(
      '📊 Full response data:',
      JSON.stringify(reportResponse.data, null, 2)
    );

    if (!pdfUrl) {
      // Try to construct the URL from the logs
      const analysisId = reportResponse.data.data.analysisId;
      const constructedUrl = `/api/report/download-from-storage/${analysisId}/reports%2Freport-${analysisId}-xNpNna-ImuDPao-YhUhkk.pdf`;
      console.log('🔧 Trying constructed URL:', constructedUrl);

      // Test if we can access the PDF directly
      const testResponse = await axios.head(
        `http://localhost:5000${constructedUrl}`
      );
      if (testResponse.status === 200) {
        console.log('✅ PDF accessible via constructed URL');
        // Use the constructed URL for download
        const pdfDownloadResponse = await axios.get(
          `http://localhost:5000${constructedUrl}`,
          {
            responseType: 'arraybuffer',
          }
        );

        fs.writeFileSync('test-report.pdf', pdfDownloadResponse.data);
        console.log('✅ PDF downloaded as test-report.pdf');
        console.log(
          '📊 PDF size:',
          (pdfDownloadResponse.data.length / 1024).toFixed(2),
          'KB'
        );

        console.log('\n🎉 Complete flow test successful!');
        console.log('\n📋 Summary:');
        console.log('- Analysis ID:', analysisId);
        console.log(
          '- MVP Score:',
          uploadResponse.data.scoreData.total,
          '/ 40 points'
        );
        console.log('- Category:', uploadResponse.data.scoreData.category);
        console.log('- PDF Generated: test-report.pdf');
        console.log(
          '- Models Used: GPT-4o and o1-preview for enhanced reasoning'
        );
        return;
      }

      throw new Error(
        'PDF URL not generated and constructed URL not accessible'
      );
    }

    // Step 4: Download PDF to verify
    console.log('\n⬇️  Step 4: Downloading PDF to verify...');

    const pdfDownloadResponse = await axios.get(
      `http://localhost:5000${pdfUrl}`,
      {
        responseType: 'arraybuffer',
      }
    );

    fs.writeFileSync('test-report.pdf', pdfDownloadResponse.data);
    console.log('✅ PDF downloaded as test-report.pdf');
    console.log(
      '📊 PDF size:',
      (pdfDownloadResponse.data.length / 1024).toFixed(2),
      'KB'
    );

    console.log('\n🎉 Complete flow test successful!');
    console.log('\n📋 Summary:');
    console.log('- Analysis ID:', analysisId);
    console.log(
      '- MVP Score:',
      uploadResponse.data.scoreData.total,
      '/ 40 points'
    );
    console.log('- Category:', uploadResponse.data.scoreData.category);
    console.log('- PDF Generated: test-report.pdf');
    console.log('- Models Used: GPT-4o and o1-preview for enhanced reasoning');
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testFullFlow();
