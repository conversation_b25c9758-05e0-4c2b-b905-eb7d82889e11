CREATE TABLE "analyses" (
	"id" serial PRIMARY KEY NOT NULL,
	"type" text NOT NULL,
	"repo_url" text,
	"description" text,
	"project_type" text NOT NULL,
	"status" text NOT NULL,
	"progress" json,
	"project_info" json,
	"score_data" json,
	"full_report" json,
	"report_url" text,
	"report_object_key" text,
	"uploaded_file_key" text,
	"extracted_path" text,
	"payment_status" text DEFAULT 'pending',
	"user_id" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"username" text,
	"password" text,
	"google_id" text,
	"email" text,
	"name" text,
	"avatar_url" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_username_unique" UNIQUE("username"),
	CONSTRAINT "users_google_id_unique" UNIQUE("google_id"),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
