{"id": "fc24b0b3-41d6-4079-847b-88019b2ef6ff", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.analyses": {"name": "analyses", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "repo_url": {"name": "repo_url", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "project_type": {"name": "project_type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "progress": {"name": "progress", "type": "json", "primaryKey": false, "notNull": false}, "project_info": {"name": "project_info", "type": "json", "primaryKey": false, "notNull": false}, "score_data": {"name": "score_data", "type": "json", "primaryKey": false, "notNull": false}, "full_report": {"name": "full_report", "type": "json", "primaryKey": false, "notNull": false}, "report_url": {"name": "report_url", "type": "text", "primaryKey": false, "notNull": false}, "report_object_key": {"name": "report_object_key", "type": "text", "primaryKey": false, "notNull": false}, "uploaded_file_key": {"name": "uploaded_file_key", "type": "text", "primaryKey": false, "notNull": false}, "extracted_path": {"name": "extracted_path", "type": "text", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "google_id": {"name": "google_id", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_google_id_unique": {"name": "users_google_id_unique", "nullsNotDistinct": false, "columns": ["google_id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}