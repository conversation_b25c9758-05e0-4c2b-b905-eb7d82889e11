{"name": "test-mvp-project", "version": "1.0.0", "description": "A test MVP project for report generation", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "build": "webpack --mode production"}, "dependencies": {"express": "^4.18.2", "react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.4.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "webpack": "^5.88.0"}, "keywords": ["mvp", "test", "project"], "author": "Test Developer", "license": "MIT"}