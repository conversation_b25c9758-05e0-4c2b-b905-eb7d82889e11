# Security Analysis

## Project: {{project_name}}

## Security-Critical Files
{{#each files}}
{{#if (contains path "auth")}}
### {{path}}
```{{extension}}
{{content}}
```
{{/if}}
{{#if (contains path "security")}}
### {{path}}
```{{extension}}
{{content}}
```
{{/if}}
{{#if (contains path "config")}}
### {{path}}
```{{extension}}
{{content}}
```
{{/if}}
{{/each}}

## Security Analysis Request
Conduct a comprehensive security analysis focusing on:
1. Authentication and authorization mechanisms
2. Data protection and encryption
3. Input validation and sanitization
4. API security measures
5. Dependency vulnerabilities
6. Configuration security
7. Error handling and information disclosure
8. Session management
9. CSRF and XSS protection
10. SQL injection prevention

Provide specific recommendations for each identified issue.
